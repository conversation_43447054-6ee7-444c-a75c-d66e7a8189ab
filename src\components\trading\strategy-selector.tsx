/**
 * Strategy Selector Component
 * Allows users to switch between EMA_SCALPER and ORB strategies
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Settings, 
  TrendingUp, 
  Target, 
  Activity,
  ChevronDown,
  Check
} from 'lucide-react';
import { TradingConfiguration } from '@/lib/trading-config';
import { ORBStrategyConfig } from './ORBStrategyConfig';
import { ORBConfig } from '@/lib/strategies/orbStrategy';

interface StrategySelectorProps {
  config: TradingConfiguration | null;
  loading?: boolean;
  onStrategyChange: (strategyName: 'EMA_SCALPER' | 'ORB') => Promise<void>;
  onConfigUpdate: (updates: Partial<TradingConfiguration>) => Promise<void>;
}

export function StrategySelector({ 
  config, 
  loading = false, 
  onStrategyChange,
  onConfigUpdate 
}: StrategySelectorProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [switching, setSwitching] = useState(false);

  const strategies = [
    {
      name: 'EMA_SCALPER' as const,
      displayName: 'EMA Scalper',
      description: 'Fast-moving exponential moving average scalping strategy',
      icon: TrendingUp,
      color: 'bg-blue-500',
      features: ['Quick entries', 'Short-term trades', 'High frequency']
    },
    {
      name: 'ORB' as const,
      displayName: 'Opening Range Breakout',
      description: 'Breakout strategy based on opening range analysis',
      icon: Target,
      color: 'bg-purple-500',
      features: ['Range breakouts', 'Session-based', 'Multiple timeframes']
    }
  ];

  const currentStrategy = strategies.find(s => s.name === config?.strategyName);

  const handleStrategySwitch = async (strategyName: 'EMA_SCALPER' | 'ORB') => {
    if (switching || !config || config.strategyName === strategyName) return;

    setSwitching(true);
    try {
      await onStrategyChange(strategyName);
    } catch (error) {
      console.error('Failed to switch strategy:', error);
    } finally {
      setSwitching(false);
    }
  };

  const handleORBConfigChange = async (orbConfig: ORBConfig) => {
    if (!config) return;

    try {
      await onConfigUpdate({
        strategyParams: {
          ...config.strategyParams,
          // ORB specific parameters
          orbTime: orbConfig.orbTime,
          sensitivity: orbConfig.sensitivity,
          breakoutCondition: orbConfig.breakoutCondition,
          tpMethod: orbConfig.tpMethod,
          emaLengthORB: orbConfig.emaLength,
          slMethod: orbConfig.slMethod,
          adaptiveSL: orbConfig.adaptiveSL,
          stopLossPercent: orbConfig.stopLossPercent,
          atrTP1Mult: orbConfig.atrTP1Mult,
          atrTP2Mult: orbConfig.atrTP2Mult,
          atrTP3Mult: orbConfig.atrTP3Mult,
          atrPeriod: orbConfig.atrPeriod,
          minimumProfitPercent: orbConfig.minimumProfitPercent,
          minimumProfitIncrementPercent: orbConfig.minimumProfitIncrementPercent,
          customSessionEnabled: orbConfig.customSessionEnabled,
          customSession: orbConfig.customSession
        }
      });
    } catch (error) {
      console.error('Failed to update ORB configuration:', error);
    }
  };

  if (loading || !config) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Strategy Configuration</span>
          </CardTitle>
          <CardDescription>Loading strategy settings...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-700 rounded w-3/4"></div>
            <div className="h-4 bg-gray-700 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Strategy Configuration</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-gray-400 hover:text-white"
          >
            <ChevronDown className={`h-4 w-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
          </Button>
        </CardTitle>
        <CardDescription>
          Select and configure your trading strategy
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Strategy Display */}
        <div className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg border border-gray-700">
          <div className="flex items-center space-x-3">
            {currentStrategy && (
              <>
                <div className={`p-2 rounded-lg ${currentStrategy.color}`}>
                  <currentStrategy.icon className="h-4 w-4 text-white" />
                </div>
                <div>
                  <div className="font-medium text-white">{currentStrategy.displayName}</div>
                  <div className="text-sm text-gray-400">{currentStrategy.description}</div>
                </div>
              </>
            )}
          </div>
          <Badge variant="secondary" className="bg-green-900/20 text-green-300 border-green-800">
            Active
          </Badge>
        </div>

        {/* Strategy Selection */}
        {isExpanded && (
          <div className="space-y-4">
            <div className="text-sm font-medium text-gray-300">Available Strategies</div>
            <div className="grid grid-cols-1 gap-3">
              {strategies.map((strategy) => {
                const isActive = config.strategyName === strategy.name;
                const IconComponent = strategy.icon;
                
                return (
                  <div
                    key={strategy.name}
                    className={`p-4 rounded-lg border cursor-pointer transition-all ${
                      isActive 
                        ? 'border-blue-500 bg-blue-900/20' 
                        : 'border-gray-700 bg-gray-800/30 hover:border-gray-600'
                    }`}
                    onClick={() => handleStrategySwitch(strategy.name)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-lg ${strategy.color}`}>
                          <IconComponent className="h-4 w-4 text-white" />
                        </div>
                        <div>
                          <div className="font-medium text-white">{strategy.displayName}</div>
                          <div className="text-sm text-gray-400">{strategy.description}</div>
                          <div className="flex space-x-2 mt-1">
                            {strategy.features.map((feature) => (
                              <Badge key={feature} variant="outline" className="text-xs">
                                {feature}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                      {isActive && (
                        <Check className="h-5 w-5 text-blue-400" />
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Strategy-Specific Configuration */}
        {config.strategyName === 'ORB' && isExpanded && (
          <div className="border-t border-gray-700 pt-6">
            <div className="text-sm font-medium text-gray-300 mb-4">ORB Strategy Settings</div>
            <ORBStrategyConfig
              config={{
                orbTime: config.strategyParams.orbTime || "30",
                sensitivity: config.strategyParams.sensitivity || "Medium",
                breakoutCondition: config.strategyParams.breakoutCondition || "Close",
                tpMethod: config.strategyParams.tpMethod || "Dynamic",
                emaLength: config.strategyParams.emaLengthORB || 9,
                slMethod: config.strategyParams.slMethod || "Balanced",
                adaptiveSL: config.strategyParams.adaptiveSL ?? true,
                stopLossPercent: config.strategyParams.stopLossPercent || 1.0,
                atrTP1Mult: config.strategyParams.atrTP1Mult || 0.75,
                atrTP2Mult: config.strategyParams.atrTP2Mult || 1.5,
                atrTP3Mult: config.strategyParams.atrTP3Mult || 2.25,
                atrPeriod: config.strategyParams.atrPeriod || 12,
                minimumProfitPercent: config.strategyParams.minimumProfitPercent || 0.2,
                minimumProfitIncrementPercent: config.strategyParams.minimumProfitIncrementPercent || 0.075,
                customSessionEnabled: config.strategyParams.customSessionEnabled || false,
                customSession: config.strategyParams.customSession || "1000-1100"
              }}
              onConfigChange={handleORBConfigChange}
              disabled={switching}
            />
          </div>
        )}

        {/* EMA Scalper Configuration */}
        {config.strategyName === 'EMA_SCALPER' && isExpanded && (
          <div className="border-t border-gray-700 pt-6">
            <div className="text-sm font-medium text-gray-300 mb-4">EMA Scalper Settings</div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">
                  EMA Length
                </label>
                <input
                  type="number"
                  value={config.strategyParams.emaLength || 20}
                  onChange={(e) => onConfigUpdate({
                    strategyParams: {
                      ...config.strategyParams,
                      emaLength: parseInt(e.target.value)
                    }
                  })}
                  min="1"
                  max="100"
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">
                  Lookback Period
                </label>
                <input
                  type="number"
                  value={config.strategyParams.lookbackPeriod || 8}
                  onChange={(e) => onConfigUpdate({
                    strategyParams: {
                      ...config.strategyParams,
                      lookbackPeriod: parseInt(e.target.value)
                    }
                  })}
                  min="1"
                  max="50"
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>
        )}

        {switching && (
          <div className="flex items-center justify-center p-4 bg-blue-900/20 border border-blue-800 rounded-lg">
            <Activity className="h-4 w-4 text-blue-400 animate-spin mr-2" />
            <span className="text-blue-300">Switching strategy...</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
