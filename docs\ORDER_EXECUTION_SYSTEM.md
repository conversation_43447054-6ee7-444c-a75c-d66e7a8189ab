# Order Execution System Documentation

## Overview

The trading platform includes a comprehensive automatic order execution system that processes market data, generates trading signals, and automatically places orders when strategy conditions are met. This system works with both EMA_SCALPER and ORB strategies and supports both sandbox and live trading modes.

## System Architecture

### 1. Market Data Flow
```
WebSocket Market Data → useMarketData Hook → Dashboard → processMarketData → RealTimeTradingService
```

### 2. Signal Generation Flow
```
Market Data → Strategy Service → Signal Generation → Signal Storage → Callback Triggers
```

### 3. Order Execution Flow
```
Trading Signal → Signal Validation → Order Preparation → API Call → Order Placement → Result Processing
```

## Key Components

### 1. RealTimeTradingService (`src/lib/real-time-trading.ts`)
- **Purpose**: Orchestrates the entire trading process
- **Key Methods**:
  - `processMarketData()`: Processes incoming market data
  - `handleSignal()`: Validates and processes trading signals
  - `executeTrade()`: Places orders through the API
  - `start()` / `stop()`: Controls service lifecycle

### 2. Strategy Service (`src/lib/strategies/strategy-service.ts`)
- **Purpose**: Generates trading signals based on market data
- **Strategies Supported**:
  - EMA_SCALPER: Fast-moving exponential moving average scalping
  - ORB: Opening Range Breakout strategy
- **Key Methods**:
  - `processMarketData()`: Analyzes market data and generates signals
  - `processEMAStrategy()`: EMA scalping signal generation
  - `processORBStrategy()`: ORB signal generation

### 3. Order Placement API (`src/app/api/trading/place-order/route.ts`)
- **Purpose**: Handles actual order placement
- **Features**:
  - Supports both sandbox and live trading
  - Validates trading limits and permissions
  - Prevents duplicate orders using correlation IDs
  - Integrates with Dhan API for live orders
  - Uses sandbox service for testing

### 4. Sandbox Trading Service (`src/lib/sandbox-trading.ts`)
- **Purpose**: Simulates order execution for testing
- **Features**:
  - Realistic order simulation with slippage
  - Configurable success/failure rates
  - Latency simulation
  - Risk-free testing environment

## Order Execution Process

### Step 1: Market Data Processing
1. WebSocket receives real-time market data
2. Data is processed and validated
3. Market data is passed to the trading service

### Step 2: Signal Generation
1. Strategy service analyzes market data
2. Applies strategy-specific logic (EMA or ORB)
3. Generates BUY/SELL signals when conditions are met
4. Signals are stored in database and callbacks triggered

### Step 3: Signal Validation
1. Checks if auto-trade is enabled
2. Validates trading limits (daily trade count, etc.)
3. Ensures instrument is enabled for trading
4. Verifies user permissions

### Step 4: Order Preparation
1. Generates unique correlation ID for idempotency
2. Retrieves instrument details (security ID, exchange)
3. Calculates quantity based on configuration
4. Prepares order request with all required parameters

### Step 5: Order Placement
1. Calls `/api/trading/place-order` endpoint
2. For SANDBOX mode: Uses sandbox trading service
3. For LIVE mode: Calls Dhan API directly
4. Handles success/failure responses

### Step 6: Result Processing
1. Updates signal execution status
2. Stores trade record in database
3. Updates trading statistics
4. Triggers callbacks for UI updates

## Configuration and Settings

### Trading Configuration
- **Auto-trade enabled/disabled**: Controls automatic execution
- **Trading mode**: SANDBOX or LIVE
- **Strategy selection**: EMA_SCALPER or ORB
- **Quantity settings**: Default order quantities
- **Risk management**: Daily trade limits, stop-loss settings

### Strategy Parameters

#### EMA_SCALPER
- `emaLength`: EMA period (default: 20)
- `lookbackPeriod`: Historical data lookback (default: 8)
- `defaultQuantity`: Order size (default: 1)

#### ORB Strategy
- `orbTime`: Opening range duration (default: "30" minutes)
- `sensitivity`: Breakout sensitivity ("Low", "Medium", "High")
- `breakoutCondition`: Entry condition ("Close", "Touch")
- `tpMethod`: Take profit method ("Fixed", "Dynamic")
- `slMethod`: Stop loss method ("Fixed", "Balanced", "Aggressive")

### Instrument Configuration
- **Enabled instruments**: Which symbols to trade
- **Security IDs**: Correct Dhan API security identifiers
- **Exchange segments**: NSE_I for indices, NSE_EQ for equities

## Safety Features

### 1. Risk Management
- Daily trade limits to prevent over-trading
- Position size controls
- Stop-loss and take-profit mechanisms
- Trading hours validation

### 2. Error Handling
- Comprehensive error logging
- Graceful failure handling
- Retry mechanisms for transient failures
- User notification of critical errors

### 3. Validation
- Duplicate order prevention using correlation IDs
- Input validation for all order parameters
- Trading permission checks
- Market hours validation

### 4. Testing
- Sandbox mode for risk-free testing
- Comprehensive test coverage
- Order simulation with realistic conditions
- Performance monitoring

## Usage Examples

### 1. Enable Automatic Trading
```typescript
// Enable auto-trade through the dashboard
await toggleAutoTrade(); // Enables/disables automatic execution

// Or programmatically
await tradingConfigService.toggleAutoTrade(userId);
```

### 2. Switch Trading Strategies
```typescript
// Switch to ORB strategy
await switchStrategy('ORB');

// Switch to EMA Scalper
await switchStrategy('EMA_SCALPER');
```

### 3. Configure Strategy Parameters
```typescript
// Update ORB strategy settings
await updateConfig({
  strategyParams: {
    orbTime: "45",
    sensitivity: "High",
    breakoutCondition: "Touch"
  }
});
```

### 4. Monitor Order Execution
```typescript
// Set up callbacks to monitor execution
const tradingService = new RealTimeTradingService({
  userId: 'user-123',
  onSignalGenerated: (signal) => {
    console.log('Signal generated:', signal);
  },
  onTradeExecuted: (trade) => {
    console.log('Trade executed:', trade);
  },
  onError: (error) => {
    console.error('Trading error:', error);
  }
});
```

## Monitoring and Logging

### 1. Trading Logs
- All trading activities are logged
- Signal generation events
- Order placement attempts
- Execution results
- Error conditions

### 2. Performance Metrics
- Signal generation frequency
- Order execution success rate
- Average execution time
- P&L tracking

### 3. System Health
- WebSocket connection status
- API response times
- Error rates
- Market data quality

## Troubleshooting

### Common Issues

1. **No signals generated**
   - Check if auto-trade is enabled
   - Verify market data is flowing
   - Ensure strategy parameters are correct

2. **Orders not executing**
   - Check trading limits
   - Verify instrument is enabled
   - Confirm API credentials

3. **Incorrect market data**
   - Verify security IDs are correct
   - Check exchange segment mappings
   - Validate WebSocket connection

### Debug Steps

1. Check trading configuration
2. Monitor console logs for errors
3. Verify market data flow
4. Test in sandbox mode first
5. Check API credentials and permissions

## Security Considerations

1. **API Keys**: Secure storage of Dhan API credentials
2. **User Authentication**: Proper user session management
3. **Data Validation**: Input sanitization and validation
4. **Rate Limiting**: API call rate limiting
5. **Audit Trail**: Complete logging of all trading activities

This order execution system provides a robust, scalable, and safe foundation for automated trading while maintaining flexibility for different strategies and risk management approaches.
