/**
 * WebSocket Market Data Service Tests
 * Tests for critical NIFTY 50 data mapping fixes
 */

import { DhanWebSocketMarketDataService } from '../websocket-market-data';
import { ProcessedMarketData } from '@/types/dhan';

// Mock WebSocket for testing
class MockWebSocket {
  onopen: ((event: Event) => void) | null = null;
  onmessage: ((event: MessageEvent) => void) | null = null;
  onerror: ((event: Event) => void) | null = null;
  onclose: ((event: CloseEvent) => void) | null = null;
  readyState: number = WebSocket.CONNECTING;

  constructor(public url: string) {}

  send(data: string | ArrayBuffer) {
    // Mock send implementation
  }

  close() {
    this.readyState = WebSocket.CLOSED;
    if (this.onclose) {
      this.onclose(new CloseEvent('close'));
    }
  }
}

// Mock global WebSocket
(global as any).WebSocket = MockWebSocket;

describe('DhanWebSocketMarketDataService', () => {
  let service: DhanWebSocketMarketDataService;
  let mockConfig: any;

  beforeEach(() => {
    mockConfig = {
      accessToken: 'test-token',
      clientId: 'test-client',
      reconnectInterval: 5000,
      maxReconnectAttempts: 3,
      heartbeatInterval: 30000,
      version: 2,
      authType: 2
    };

    service = new DhanWebSocketMarketDataService(mockConfig);
  });

  afterEach(() => {
    service.disconnect();
  });

  describe('Instrument Mapping', () => {
    test('should correctly map NIFTY to NSE_I_13', () => {
      // Access private method for testing
      const instrumentMap = (service as any).instrumentMap;
      
      // Initialize the instrument map
      (service as any).initializeInstrumentMap();
      
      // Verify NIFTY mapping
      const niftyMapping = instrumentMap.get('NSE_I_13');
      expect(niftyMapping).toBeDefined();
      expect(niftyMapping.symbol).toBe('NIFTY');
      expect(niftyMapping.name).toBe('NIFTY 50');
    });

    test('should correctly map BANKNIFTY to NSE_I_25', () => {
      const instrumentMap = (service as any).instrumentMap;
      (service as any).initializeInstrumentMap();
      
      const bankNiftyMapping = instrumentMap.get('NSE_I_25');
      expect(bankNiftyMapping).toBeDefined();
      expect(bankNiftyMapping.symbol).toBe('BANKNIFTY');
      expect(bankNiftyMapping.name).toBe('BANK NIFTY');
    });
  });

  describe('Subscription Mapping', () => {
    test('should convert NIFTY symbol to correct Dhan format', () => {
      // Test the subscription conversion logic by checking the mapping directly
      const testConversion = (symbol: string) => {
        switch (symbol) {
          case 'NIFTY':
            return { exchangeSegment: 'NSE_I', securityId: '13' };
          case 'BANKNIFTY':
            return { exchangeSegment: 'NSE_I', securityId: '25' };
          case 'SILVER':
            return { exchangeSegment: 'MCX_COMM', securityId: '242' };
          default:
            return null;
        }
      };

      const niftyMapping = testConversion('NIFTY');
      expect(niftyMapping).toEqual({
        exchangeSegment: 'NSE_I',
        securityId: '13'
      });
    });

    test('should handle unknown instruments gracefully', () => {
      const testConversion = (symbol: string) => {
        switch (symbol) {
          case 'NIFTY':
            return { exchangeSegment: 'NSE_I', securityId: '13' };
          case 'BANKNIFTY':
            return { exchangeSegment: 'NSE_I', securityId: '25' };
          case 'SILVER':
            return { exchangeSegment: 'MCX_COMM', securityId: '242' };
          default:
            return null;
        }
      };

      const unknownMapping = testConversion('UNKNOWN_SYMBOL');
      expect(unknownMapping).toBeNull();
    });
  });

  describe('Market Data Validation', () => {
    test('should validate NIFTY market data correctly', () => {
      const validNiftyData: ProcessedMarketData = {
        symbol: 'NIFTY',
        name: 'NIFTY 50',
        currentPrice: 24500.00,
        change: 100.50,
        changePercent: 0.41,
        isPositive: true,
        lastUpdated: new Date(),
        formattedPrice: '24,500.00',
        formattedChange: '+100.50',
        formattedChangePercent: '+0.41%'
      };

      const isValid = (service as any).validateMarketData(validNiftyData);
      expect(isValid).toBe(true);
    });

    test('should reject NIFTY data with invalid price range', () => {
      const invalidNiftyData: ProcessedMarketData = {
        symbol: 'NIFTY',
        name: 'NIFTY 50',
        currentPrice: 100000.00, // Too high
        change: 100.50,
        changePercent: 0.41,
        isPositive: true,
        lastUpdated: new Date(),
        formattedPrice: '100,000.00',
        formattedChange: '+100.50',
        formattedChangePercent: '+0.41%'
      };

      const isValid = (service as any).validateMarketData(invalidNiftyData);
      expect(isValid).toBe(false);
    });

    test('should reject data with invalid structure', () => {
      const invalidData: ProcessedMarketData = {
        symbol: '',
        name: 'NIFTY 50',
        currentPrice: -100, // Negative price
        change: 100.50,
        changePercent: 0.41,
        isPositive: true,
        lastUpdated: new Date(),
        formattedPrice: '-100.00',
        formattedChange: '+100.50',
        formattedChangePercent: '+0.41%'
      };

      const isValid = (service as any).validateMarketData(invalidData);
      expect(isValid).toBe(false);
    });
  });

  describe('Configuration Consistency', () => {
    test('should use consistent NIFTY symbol across all configurations', () => {
      // This test ensures that all parts of the system use the same symbol
      const instrumentMap = (service as any).instrumentMap;
      (service as any).initializeInstrumentMap();

      // Check instrument map uses NIFTY
      const niftyMapping = instrumentMap.get('NSE_I_13');
      expect(niftyMapping.symbol).toBe('NIFTY');

      // Check subscription conversion logic consistency
      const testConversion = (symbol: string) => {
        switch (symbol) {
          case 'NIFTY':
            return { exchangeSegment: 'NSE_I', securityId: '13' };
          default:
            return null;
        }
      };

      const niftySubscription = testConversion('NIFTY');
      expect(niftySubscription?.securityId).toBe('13');
      expect(niftySubscription?.exchangeSegment).toBe('NSE_I');
    });
  });
});
