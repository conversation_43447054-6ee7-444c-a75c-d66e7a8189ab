/**
 * Order Execution System Tests
 * Tests the complete order execution flow from signal generation to order placement
 */

import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { RealTimeTradingService } from '../real-time-trading';
import { ProcessedMarketData } from '@/types/dhan';
import { TradingSignal } from '../strategies/strategy-service';

// Mock fetch for API calls
global.fetch = jest.fn();

// Mock Supabase
jest.mock('../supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(() => Promise.resolve({ data: null, error: null }))
        }))
      }))
    }))
  }
}));

// Mock dependencies
jest.mock('../strategies/strategy-service', () => ({
  strategyService: {
    processMarketData: jest.fn()
  }
}));

jest.mock('../trading-config', () => ({
  tradingConfigService: {
    canPlaceTrade: jest.fn(),
    getEnabledInstruments: jest.fn()
  }
}));

jest.mock('../trading-logger', () => ({
  tradingLogger: {
    logSystem: jest.fn(),
    logTradeAttempt: jest.fn(),
    logTradeSuccess: jest.fn(),
    logTradeFailure: jest.fn()
  }
}));

describe('Order Execution System', () => {
  let tradingService: RealTimeTradingService;
  let mockOnSignalGenerated: jest.Mock;
  let mockOnTradeExecuted: jest.Mock;
  let mockOnError: jest.Mock;

  const mockMarketData: ProcessedMarketData = {
    symbol: 'NIFTY',
    name: 'NIFTY 50',
    currentPrice: 24500.75,
    change: 125.50,
    changePercent: 0.51,
    volume: 1500000,
    lastUpdated: new Date(),
    high: 24600.00,
    low: 24400.00,
    open: 24450.00,
    close: 24500.75,
    exchange: 'NSE_I',
    securityId: 13
  };

  const mockTradingSignal: TradingSignal = {
    id: 'test-signal-123',
    userId: 'test-user-123',
    signalType: 'BUY',
    instrumentSymbol: 'NIFTY',
    instrumentName: 'NIFTY 50',
    signalPrice: 24500.75,
    strategyName: 'EMA_SCALPER',
    strategyParams: {
      emaLength: 20,
      lookbackPeriod: 8,
      defaultQuantity: 1
    },
    marketData: {
      currentPrice: 24500.75,
      change: 125.50,
      changePercent: 0.51,
      volume: 1500000,
      lastUpdated: new Date()
    },
    candleIndex: 10,
    emaValue: 24480.25,
    last8hHigh: 24600.00,
    last8lLow: 24400.00,
    isExecuted: false,
    executionAttempted: false,
    autoTradeEnabled: true,
    tradingMode: 'SANDBOX'
  };

  beforeEach(() => {
    mockOnSignalGenerated = jest.fn();
    mockOnTradeExecuted = jest.fn();
    mockOnError = jest.fn();

    tradingService = new RealTimeTradingService({
      userId: 'test-user-123',
      onSignalGenerated: mockOnSignalGenerated,
      onTradeExecuted: mockOnTradeExecuted,
      onError: mockOnError
    });

    // Reset fetch mock
    (global.fetch as jest.Mock).mockReset();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Market Data Processing', () => {
    it('should process market data when service is active', async () => {
      // Mock strategy service to return a signal
      const { strategyService } = require('../strategies/strategy-service');
      strategyService.processMarketData = jest.fn().mockResolvedValue([mockTradingSignal]);

      // Mock trading config service
      const { tradingConfigService } = require('../trading-config');
      tradingConfigService.canPlaceTrade = jest.fn().mockResolvedValue(true);
      tradingConfigService.getEnabledInstruments = jest.fn().mockResolvedValue([
        { symbol: 'NIFTY', name: 'NIFTY 50', exchange: 'NSE_I', securityId: 13 }
      ]);

      // Mock successful order placement
      (global.fetch as jest.Mock).mockResolvedValue({
        json: () => Promise.resolve({
          success: true,
          data: {
            orderId: 'test-order-123',
            status: 'PLACED',
            isSuccessful: true
          }
        })
      });

      // Start service and process market data
      tradingService.start();
      await tradingService.processMarketData(mockMarketData);

      // Verify signal was generated
      expect(mockOnSignalGenerated).toHaveBeenCalledWith(mockTradingSignal);

      // Verify order was placed
      expect(global.fetch).toHaveBeenCalledWith('/api/trading/place-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: expect.stringContaining('"orderType":"BUY"')
      });

      // Verify trade execution callback
      expect(mockOnTradeExecuted).toHaveBeenCalledWith({
        orderId: 'test-order-123',
        status: 'PLACED',
        isSuccessful: true
      });
    });

    it('should not process market data when service is inactive', async () => {
      const { strategyService } = require('../strategies/strategy-service');
      strategyService.processMarketData = jest.fn().mockResolvedValue([mockTradingSignal]);

      // Service is not started (inactive)
      await tradingService.processMarketData(mockMarketData);

      // Verify no processing occurred
      expect(strategyService.processMarketData).not.toHaveBeenCalled();
      expect(mockOnSignalGenerated).not.toHaveBeenCalled();
    });

    it('should respect processing interval to avoid too frequent signals', async () => {
      const { strategyService } = require('../strategies/strategy-service');
      strategyService.processMarketData = jest.fn().mockResolvedValue([mockTradingSignal]);

      tradingService.start();

      // Process market data twice in quick succession
      await tradingService.processMarketData(mockMarketData);
      await tradingService.processMarketData(mockMarketData);

      // Verify strategy service was only called once due to processing interval
      expect(strategyService.processMarketData).toHaveBeenCalledTimes(1);
    });
  });

  describe('Signal Handling', () => {
    it('should not execute trade when auto-trade is disabled', async () => {
      const disabledSignal = { ...mockTradingSignal, autoTradeEnabled: false };
      
      const { strategyService } = require('../strategies/strategy-service');
      strategyService.processMarketData = jest.fn().mockResolvedValue([disabledSignal]);

      tradingService.start();
      await tradingService.processMarketData(mockMarketData);

      // Verify signal was generated but no order was placed
      expect(mockOnSignalGenerated).toHaveBeenCalledWith(disabledSignal);
      expect(global.fetch).not.toHaveBeenCalled();
    });

    it('should not execute trade when trading limits are reached', async () => {
      const { strategyService } = require('../strategies/strategy-service');
      const { tradingConfigService } = require('../trading-config');
      
      strategyService.processMarketData = jest.fn().mockResolvedValue([mockTradingSignal]);
      tradingConfigService.canPlaceTrade = jest.fn().mockResolvedValue(false);

      tradingService.start();
      await tradingService.processMarketData(mockMarketData);

      // Verify signal was generated but no order was placed
      expect(mockOnSignalGenerated).toHaveBeenCalledWith(mockTradingSignal);
      expect(global.fetch).not.toHaveBeenCalled();
    });

    it('should not execute trade when instrument is not enabled', async () => {
      const { strategyService } = require('../strategies/strategy-service');
      const { tradingConfigService } = require('../trading-config');
      
      strategyService.processMarketData = jest.fn().mockResolvedValue([mockTradingSignal]);
      tradingConfigService.canPlaceTrade = jest.fn().mockResolvedValue(true);
      tradingConfigService.getEnabledInstruments = jest.fn().mockResolvedValue([]); // No enabled instruments

      tradingService.start();
      await tradingService.processMarketData(mockMarketData);

      // Verify signal was generated but no order was placed
      expect(mockOnSignalGenerated).toHaveBeenCalledWith(mockTradingSignal);
      expect(global.fetch).not.toHaveBeenCalled();
    });
  });

  describe('Order Execution', () => {
    beforeEach(() => {
      const { strategyService } = require('../strategies/strategy-service');
      const { tradingConfigService } = require('../trading-config');
      
      strategyService.processMarketData = jest.fn().mockResolvedValue([mockTradingSignal]);
      tradingConfigService.canPlaceTrade = jest.fn().mockResolvedValue(true);
      tradingConfigService.getEnabledInstruments = jest.fn().mockResolvedValue([
        { symbol: 'NIFTY', name: 'NIFTY 50', exchange: 'NSE_I', securityId: 13 }
      ]);
    });

    it('should handle successful order execution', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        json: () => Promise.resolve({
          success: true,
          data: {
            orderId: 'test-order-123',
            status: 'TRADED',
            isSuccessful: true
          }
        })
      });

      tradingService.start();
      await tradingService.processMarketData(mockMarketData);

      expect(mockOnTradeExecuted).toHaveBeenCalledWith({
        orderId: 'test-order-123',
        status: 'TRADED',
        isSuccessful: true
      });
    });

    it('should handle failed order execution', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        json: () => Promise.resolve({
          success: false,
          error: 'Insufficient funds'
        })
      });

      tradingService.start();
      await tradingService.processMarketData(mockMarketData);

      expect(mockOnError).toHaveBeenCalledWith(expect.any(Error));
    });

    it('should handle network errors during order placement', async () => {
      (global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

      tradingService.start();
      await tradingService.processMarketData(mockMarketData);

      expect(mockOnError).toHaveBeenCalledWith(expect.any(Error));
    });

    it('should include correct order parameters', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        json: () => Promise.resolve({
          success: true,
          data: { orderId: 'test-order-123', status: 'PLACED', isSuccessful: true }
        })
      });

      tradingService.start();
      await tradingService.processMarketData(mockMarketData);

      const fetchCall = (global.fetch as jest.Mock).mock.calls[0];
      const requestBody = JSON.parse(fetchCall[1].body);

      expect(requestBody).toMatchObject({
        userId: 'test-user-123',
        signalId: 'test-signal-123',
        orderType: 'BUY',
        instrumentSymbol: 'NIFTY',
        instrumentName: 'NIFTY 50',
        instrumentSecurityId: '13',
        instrumentExchange: 'NSE_I',
        price: 24500.75,
        quantity: 1,
        tradingMode: 'SANDBOX'
      });

      expect(requestBody.correlationId).toBeDefined();
      expect(typeof requestBody.correlationId).toBe('string');
    });
  });

  describe('Service Lifecycle', () => {
    it('should start and stop service correctly', () => {
      expect(tradingService.getIsActive()).toBe(false);

      tradingService.start();
      expect(tradingService.getIsActive()).toBe(true);

      tradingService.stop();
      expect(tradingService.getIsActive()).toBe(false);
    });
  });
});
